'use client';

import { useState, useRef, useEffect } from 'react';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import ImportDialog from './ImportDialog';

export interface Message {
  id: string;
  type: 'text' | 'image';
  content: string;
  sender: string;
  avatar?: string;
  timestamp: number;
  isOwn?: boolean;
}

export default function ChatRoom() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [importedMessages, setImportedMessages] = useState<Message[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const playTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = (content: string, type: 'text' | 'image' = 'text') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      type,
      content,
      sender: '我',
      timestamp: Date.now(),
      isOwn: true,
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleImportMessages = (newImportedMessages: Message[]) => {
    setImportedMessages(newImportedMessages);
    setMessages(newImportedMessages);
    setIsImportDialogOpen(false);
  };

  const handlePlayMessages = async () => {
    if (isPlaying || importedMessages.length === 0) return;

    setIsPlaying(true);
    setMessages([]);

    // 按时间戳排序消息
    const sortedMessages = [...importedMessages].sort((a, b) => a.timestamp - b.timestamp);

    // 计算消息间的时间间隔
    const playMessage = (index: number) => {
      if (index >= sortedMessages.length) {
        setIsPlaying(false);
        return;
      }

      const currentMessage = sortedMessages[index];
      setMessages(prev => [...prev, currentMessage]);

      // 计算下一条消息的延时
      let delay = 2000; // 默认2秒间隔
      if (index < sortedMessages.length - 1) {
        const nextMessage = sortedMessages[index + 1];
        const timeDiff = nextMessage.timestamp - currentMessage.timestamp;
        // 将实际时间差缩放到合理范围（1-5秒）
        delay = Math.min(Math.max(timeDiff / 1000, 1000), 5000);
      }

      playTimeoutRef.current = setTimeout(() => {
        playMessage(index + 1);
      }, delay);
    };

    // 开始播放第一条消息
    playMessage(0);
  };

  const handleStopPlaying = () => {
    if (playTimeoutRef.current) {
      clearTimeout(playTimeoutRef.current);
      playTimeoutRef.current = null;
    }
    setIsPlaying(false);
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (playTimeoutRef.current) {
        clearTimeout(playTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      {/* 聊天容器 - 手机尺寸 */}
      <div className="w-full max-w-sm bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden flex flex-col h-[600px] sm:h-[700px] relative">
        {/* 聊天头部 */}
        <ChatHeader
          onImport={() => setIsImportDialogOpen(true)}
          onPlay={handlePlayMessages}
          onStop={handleStopPlaying}
          isPlaying={isPlaying}
          hasImportedMessages={importedMessages.length > 0}
        />

        {/* 消息列表 */}
        <div className="flex-1 overflow-hidden">
          <MessageList messages={messages} />
          <div ref={messagesEndRef} />
        </div>

        {/* 输入框 */}
        <ChatInput onSendMessage={handleSendMessage} />
      </div>

      {/* 导入对话框 */}
      {isImportDialogOpen && (
        <ImportDialog
          onImport={handleImportMessages}
          onClose={() => setIsImportDialogOpen(false)}
        />
      )}
    </div>
  );
}
