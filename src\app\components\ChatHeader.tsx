interface ChatHeaderProps {
  onImport: () => void;
  onPlay: () => void;
  onStop?: () => void;
  isPlaying: boolean;
  hasImportedMessages?: boolean;
}

export default function ChatHeader({ onImport, onPlay, onStop, isPlaying, hasImportedMessages }: ChatHeaderProps) {
  return (
    <div className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600 p-4">
      <div className="flex items-center justify-between">
        {/* 群聊信息 */}
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
            <span className="text-white font-medium text-sm">群</span>
          </div>
          <div>
            <h1 className="font-medium text-gray-900 dark:text-white">播客聊天室</h1>
            <p className="text-xs text-gray-500 dark:text-gray-400">在线模拟聊天</p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-2">
          <button
            onClick={onImport}
            className="p-2 rounded-full bg-blue-500 hover:bg-blue-600 text-white transition-colors"
            title="导入对话"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
            </svg>
          </button>

          {!isPlaying ? (
            <button
              onClick={onPlay}
              disabled={!hasImportedMessages}
              className={`p-2 rounded-full transition-colors ${hasImportedMessages
                  ? 'bg-green-500 hover:bg-green-600 text-white'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                }`}
              title={hasImportedMessages ? '开始播放' : '请先导入对话数据'}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M15 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          ) : (
            <button
              onClick={onStop}
              className="p-2 rounded-full bg-red-500 hover:bg-red-600 text-white transition-colors"
              title="停止播放"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10h6v4H9z" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
