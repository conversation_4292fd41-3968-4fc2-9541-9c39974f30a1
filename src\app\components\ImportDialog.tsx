'use client';

import { useState } from 'react';
import { Message } from './ChatRoom';

interface ImportDialogProps {
  onImport: (messages: Message[]) => void;
  onClose: () => void;
}

export default function ImportDialog({ onImport, onClose }: ImportDialogProps) {
  const [jsonText, setJsonText] = useState('');
  const [error, setError] = useState('');

  const sampleData = `[
  {
    "id": "1",
    "type": "text",
    "content": "大家好！欢迎来到播客聊天室！",
    "sender": "主持人",
    "timestamp": 1640995200000,
    "isOwn": false
  },
  {
    "id": "2", 
    "type": "text",
    "content": "今天我们要聊的话题是人工智能的发展",
    "sender": "主持人",
    "timestamp": 1640995260000,
    "isOwn": false
  },
  {
    "id": "3",
    "type": "text", 
    "content": "这个话题很有意思！",
    "sender": "嘉宾A",
    "timestamp": 1640995320000,
    "isOwn": false
  },
  {
    "id": "4",
    "type": "text",
    "content": "我觉得AI会改变很多行业",
    "sender": "嘉宾B", 
    "timestamp": 1640995380000,
    "isOwn": false
  }
]`;

  const handleImport = () => {
    try {
      const parsed = JSON.parse(jsonText);
      
      if (!Array.isArray(parsed)) {
        throw new Error('数据格式错误：应该是一个数组');
      }

      const messages: Message[] = parsed.map((item, index) => ({
        id: item.id || `imported_${index}`,
        type: item.type || 'text',
        content: item.content || '',
        sender: item.sender || '未知用户',
        avatar: item.avatar,
        timestamp: item.timestamp || Date.now(),
        isOwn: item.isOwn || false,
      }));

      onImport(messages);
      setError('');
    } catch (err) {
      setError(err instanceof Error ? err.message : '解析JSON失败');
    }
  };

  const loadSample = () => {
    setJsonText(sampleData);
    setError('');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">导入对话数据</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容 */}
        <div className="p-4 space-y-4 overflow-y-auto max-h-[60vh]">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              JSON 格式的对话数据
            </label>
            <textarea
              value={jsonText}
              onChange={(e) => setJsonText(e.target.value)}
              placeholder="请粘贴JSON格式的对话数据..."
              className="w-full h-64 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
            />
          </div>

          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
            </div>
          )}

          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              数据格式说明：
            </p>
            <ul className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
              <li>• id: 消息唯一标识</li>
              <li>• type: 消息类型 ("text" 或 "image")</li>
              <li>• content: 消息内容</li>
              <li>• sender: 发送者名称</li>
              <li>• timestamp: 时间戳</li>
              <li>• isOwn: 是否为自己发送的消息 (可选)</li>
            </ul>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-600">
          <button
            onClick={loadSample}
            className="px-4 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
          >
            加载示例数据
          </button>
          
          <div className="flex gap-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleImport}
              disabled={!jsonText.trim()}
              className={`px-4 py-2 text-sm rounded-lg transition-colors ${
                jsonText.trim()
                  ? 'bg-blue-500 hover:bg-blue-600 text-white'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              }`}
            >
              导入
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
