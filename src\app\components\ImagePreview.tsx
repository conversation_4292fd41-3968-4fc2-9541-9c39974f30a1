'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';

interface ImagePreviewProps {
  src: string;
  alt: string;
  onClose: () => void;
}

export default function ImagePreview({ src, alt, onClose }: ImagePreviewProps) {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });

  useEffect(() => {
    // 预加载图片获取真实尺寸
    const img = new window.Image();
    img.onload = () => {
      setImageSize({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.src = src;
  }, [src]);

  useEffect(() => {
    // 阻止背景滚动
    document.body.style.overflow = 'hidden';
    
    // 监听ESC键关闭
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [onClose]);

  // 计算显示尺寸，保持宽高比
  const calculateDisplaySize = () => {
    if (!imageSize.width || !imageSize.height) return { width: 400, height: 300 };
    
    const maxWidth = window.innerWidth * 0.9;
    const maxHeight = window.innerHeight * 0.9;
    
    const aspectRatio = imageSize.width / imageSize.height;
    
    let displayWidth = imageSize.width;
    let displayHeight = imageSize.height;
    
    // 如果图片太大，按比例缩放
    if (displayWidth > maxWidth) {
      displayWidth = maxWidth;
      displayHeight = displayWidth / aspectRatio;
    }
    
    if (displayHeight > maxHeight) {
      displayHeight = maxHeight;
      displayWidth = displayHeight * aspectRatio;
    }
    
    return { width: displayWidth, height: displayHeight };
  };

  const displaySize = calculateDisplaySize();

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div className="relative max-w-full max-h-full">
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
          title="关闭 (ESC)"
        >
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        {/* 图片容器 */}
        <div 
          className="relative bg-white rounded-lg overflow-hidden shadow-2xl"
          onClick={(e) => e.stopPropagation()}
          style={{
            width: displaySize.width,
            height: displaySize.height,
          }}
        >
          <Image
            src={src}
            alt={alt}
            fill
            className="object-contain"
            sizes="90vw"
            priority
          />
        </div>
        
        {/* 图片信息 */}
        <div className="absolute -bottom-12 left-0 text-white text-sm opacity-75">
          {imageSize.width > 0 && imageSize.height > 0 && (
            <span>{imageSize.width} × {imageSize.height}</span>
          )}
        </div>
      </div>
    </div>
  );
}
