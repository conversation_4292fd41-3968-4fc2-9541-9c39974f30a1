# 播客聊天室 (Podcast Chat Room)

一个类似微信群聊的聊天界面，支持导入对话文本并实现延时输出生成，同时支持显示图片。

## 功能特性

- 🎨 **微信风格界面**: 仿微信群聊的 UI 设计，包含消息气泡、头像等元素
- 📱 **手机尺寸设计**: 中间是手机尺寸的聊天界面，两边是渐变背景
- 📥 **对话导入**: 支持 JSON 格式的对话数据导入
- ⏰ **延时播放**: 模拟真实聊天场景，按时间戳延时显示消息
- 🖼️ **图片支持**: 支持文本和图片消息的显示
- 🌙 **暗色模式**: 自动适配系统暗色模式
- 📱 **响应式设计**: 适配不同屏幕尺寸

## 快速开始

1. 安装依赖：

```bash
npm install
```

2. 启动开发服务器：

```bash
npm run dev
```

3. 打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 使用说明

### 导入对话数据

1. 点击聊天界面顶部的"导入对话"按钮（蓝色下载图标）
2. 在弹出的对话框中粘贴 JSON 格式的对话数据
3. 点击"加载示例数据"可以查看数据格式示例
4. 点击"导入"按钮完成导入

### JSON 数据格式

```json
[
  {
    "id": "1",
    "type": "text",
    "content": "消息内容",
    "sender": "发送者名称",
    "timestamp": 1640995200000,
    "isOwn": false,
    "avatar": "头像URL（可选）"
  }
]
```

字段说明：

- `id`: 消息唯一标识
- `type`: 消息类型（"text" 或 "image"）
- `content`: 消息内容（文本内容或图片 URL）
- `sender`: 发送者名称
- `timestamp`: 时间戳（毫秒）
- `isOwn`: 是否为自己发送的消息（可选，默认 false）
- `avatar`: 头像 URL（可选）

### 播放对话

1. 导入对话数据后，点击"开始播放"按钮（绿色播放图标）
2. 消息将按照时间戳顺序延时显示
3. 播放过程中可以点击"停止播放"按钮（红色停止图标）暂停

### 发送消息

- 在底部输入框中输入文本消息，按 Enter 或点击发送按钮
- 点击图片按钮可以上传并发送图片消息

## 技术栈

- **框架**: Next.js 15.5.3
- **语言**: TypeScript
- **样式**: Tailwind CSS 4.0
- **代码格式化**: Biome

## 项目结构

```
src/
├── app/
│   ├── components/
│   │   ├── ChatRoom.tsx      # 主聊天室组件
│   │   ├── ChatHeader.tsx    # 聊天头部组件
│   │   ├── MessageList.tsx   # 消息列表组件
│   │   ├── MessageBubble.tsx # 消息气泡组件
│   │   ├── ChatInput.tsx     # 输入框组件
│   │   └── ImportDialog.tsx  # 导入对话框组件
│   ├── globals.css           # 全局样式
│   ├── layout.tsx           # 根布局
│   └── page.tsx             # 首页
```

## 开发命令

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm run start

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 自定义配置

你可以通过修改以下文件来自定义应用：

- `src/app/globals.css`: 修改全局样式和主题色彩
- `src/app/components/ChatRoom.tsx`: 修改聊天室逻辑和布局
- `tailwind.config.js`: 修改 Tailwind CSS 配置（如需要）

## 许可证

MIT License
