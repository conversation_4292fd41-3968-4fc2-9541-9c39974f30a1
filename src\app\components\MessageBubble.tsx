import Image from 'next/image';
import { Message } from './ChatRoom';

interface MessageBubbleProps {
  message: Message;
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const { type, content, sender, avatar, isOwn } = message;

  return (
    <div className={`flex items-end gap-2 mb-4 message-enter ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
      {/* 头像 */}
      <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex-shrink-0 overflow-hidden">
        {avatar ? (
          <Image
            src={avatar}
            alt={sender}
            width={32}
            height={32}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-white text-xs font-medium">
            {sender.charAt(0)}
          </div>
        )}
      </div>

      {/* 消息气泡 */}
      <div className="flex flex-col max-w-[70%]">
        {/* 发送者名称 */}
        {!isOwn && (
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-1 px-1">
            {sender}
          </div>
        )}

        {/* 消息内容 */}
        <div
          className={`rounded-2xl px-4 py-2 ${isOwn
              ? 'bg-green-500 text-white rounded-br-md'
              : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-600'
            }`}
        >
          {type === 'text' ? (
            <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
              {content}
            </p>
          ) : (
            <div className="relative">
              <Image
                src={content}
                alt="图片消息"
                width={200}
                height={200}
                className="rounded-lg max-w-full h-auto"
                style={{ maxHeight: '200px', objectFit: 'cover' }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
